from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
from micu_uart_lib import (
    SimpleUART, micu_printf
)



# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lower_purple = np.array([130, 80, 80])
        upper_purple = np.array([160, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """
    对四边形进行透视变换
    :param pts: 四边形顶点坐标 (4,2)
    :param target_width: 校正后宽度
    :param target_height: 校正后高度
    :return: 变换矩阵M和逆矩阵M_inv
    """
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(180, 120, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 460800, set_as_global=True):
        print("串口初始化成功")

        # 配置串口数据接收功能
        uart.set_auto_extract(True)  # 启用自动数据提取
        uart.set_auto_refresh(False)  # 设置为累积模式，避免数据丢失
        uart.set_frame("", "", False)  # 禁用帧检测，支持多种数据格式

        print("串口数据接收功能已启用")
        print("- 自动数据提取: 已启用")
        print("- 缓冲模式: 累积模式")
        print("- 帧检测: 已禁用")

    else:
        print("串口初始化失败")
        exit()

    # 核心参数
    min_contour_area = 1000
    max_contour_area = 10000
    target_sides = 4
    
    # 透视变换与圆形参数
    corrected_width = 200    # 校正后矩形宽度
    corrected_height = 150   # 校正后矩形高度
    circle_radius = 40       # 校正后矩形内圆的半径
    circle_num_points = 12   # 圆周点数量
    
    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    
    # 绘制参数（避免干扰识别区域）
    DRAW_PADDING = 10  # 绘制边距
    FPS_POSITION = (0, 10)  # FPS显示位置(右上)
    TEXT_FONT = cv2.FONT_HERSHEY_SIMPLEX
    TEXT_SCALE = 0.5
    TEXT_THICKNESS = 1
    TEXT_COLOR = (0, 255, 0)  # 绿色

    while not app.need_exit():
        # 计算FPS
        current_time = time.ticks_ms()
        if current_time - last_time > 0:
            fps = 1000.0 / (current_time - last_time)
        last_time = current_time
        
        # 读取图像
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 只保留最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 处理内框：透视变换→画圆→映射回原图
        all_circle_points = []  # 存储所有映射回原图的圆轨迹点
        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 计算透视变换矩阵
            M, M_inv, src_pts = perspective_transform(
                pts, corrected_width, corrected_height
            )
            
            # 生成校正后矩形内的圆形轨迹（圆心为校正后矩形的中心）
            corrected_center = (corrected_width//2, corrected_height//2)
            corrected_circle = generate_circle_points(
                corrected_center, circle_radius, circle_num_points
            )
            
            # 将校正后的圆轨迹点映射回原图
            if M_inv is not None:
                # 格式转换为opencv需要的形状 (1, N, 2)
                corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                original_points = [(int(x), int(y)) for x, y in original_points]
                all_circle_points.extend(original_points)
                
                # 绘制映射回原图的轨迹点（红色）
                for (x, y) in original_points:
                    cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
            
            # 绘制内框轮廓和中心点（调试用）
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)
            M_moments = cv2.moments(approx)
            if M_moments["m00"] != 0:
                cx = int(M_moments["m10"] / M_moments["m00"])
                cy = int(M_moments["m01"] / M_moments["m00"])
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)

        # 3. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据
        # 发送内框圆轨迹点（格式：C,数量,x1,y1,x2,y2...）
#       if all_circle_points:
#            circle_data = f"C,{len(all_circle_points)}"
#            for (x, y) in all_circle_points:
#                circle_data += f",{x},{y}"
#            micu_printf(circle_data)
        
        # 发送激光点
#        if laser_points:
#            laser_data = f"L,{len(laser_points)}"
#           for (x, y) in laser_points:
#                laser_data += f",{x},{y}"
#            micu_printf(laser_data)


       # 修改Python的串口发送部分
        if all_circle_points:
            x, y = all_circle_points[0]
            mid_data = f"MID:({x},{y})\n"  # 添加换行符
            micu_printf(mid_data)

        if laser_points:
            x, y = laser_points[0]
            target_data = f"TARGET:({x},{y})\n"  # 添加换行符
            micu_printf(target_data)

        # 在右上角显示FPS，避免干扰主要识别区域
        cv2.putText(output, f"FPS: {fps:.1f}", FPS_POSITION,
                   TEXT_FONT, TEXT_SCALE, TEXT_COLOR, TEXT_THICKNESS)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)    


